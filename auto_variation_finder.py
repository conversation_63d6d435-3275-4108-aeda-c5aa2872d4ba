#!/usr/bin/env python3
"""
Enhanced Image Variation Finder and Combiner

A Python tool that automatically finds similar image variations using multiple
advanced computer vision techniques for maximum accuracy:
- CLIP (semantic similarity)
- Perceptual hashing (structural similarity)
- ORB features (keypoint matching)
- SIFT features (scale-invariant features)
"""

import os
import sys
from PIL import Image
import argparse
from typing import List, Tuple, Dict, Set, Optional
from collections import defaultdict
import glob
from image_combiner import ImageCombiner
import shutil
import numpy as np
import cv2
import hashlib

# Import required libraries with fallbacks
try:
    import torch
    import clip
    from sklearn.metrics.pairwise import cosine_similarity
    CLIP_AVAILABLE = True
except ImportError:
    CLIP_AVAILABLE = False
    print("Warning: CLIP not available. Install with: pip install torch torchvision clip-by-openai scikit-learn")

try:
    import imagehash
    IMAGEHASH_AVAILABLE = True
except ImportError:
    IMAGEHASH_AVAILABLE = False
    print("Warning: imagehash not available. Install with: pip install imagehash")

# Check if we have at least one similarity method available
if not CLIP_AVAILABLE and not IMAGEHASH_AVAILABLE:
    print("Error: No similarity detection methods available.")
    print("Install at least one of:")
    print("  - CLIP: pip install torch torchvision clip-by-openai scikit-learn")
    print("  - ImageHash: pip install imagehash")
    sys.exit(1)

class ImageVariationFinder:
    def __init__(self, clip_threshold: float = 0.85, clip_model_name: str = "ViT-B/32", device: str = "auto",
                 hash_threshold: int = 10, orb_threshold: float = 0.7, sift_threshold: float = 0.7,
                 use_clip: bool = True, use_hash: bool = True, use_orb: bool = True, use_sift: bool = False):
        """
        Initialize the enhanced variation finder with multiple similarity methods.

        Args:
            clip_threshold: Minimum cosine similarity for CLIP (0.0-1.0, higher = more strict)
            clip_model_name: CLIP model to use ("ViT-B/32", "ViT-L/14", "RN50", etc.)
            device: Device to use ("auto", "cpu", "gpu", "mps", "cuda")
            hash_threshold: Maximum hamming distance for perceptual hashes (lower = more strict)
            orb_threshold: Minimum match ratio for ORB features (0.0-1.0, higher = more strict)
            sift_threshold: Minimum match ratio for SIFT features (0.0-1.0, higher = more strict)
            use_clip: Whether to use CLIP for semantic similarity
            use_hash: Whether to use perceptual hashing for structural similarity
            use_orb: Whether to use ORB features for keypoint matching
            use_sift: Whether to use SIFT features for scale-invariant matching
        """
        self.clip_threshold = clip_threshold
        self.clip_model_name = clip_model_name
        self.hash_threshold = hash_threshold
        self.orb_threshold = orb_threshold
        self.sift_threshold = sift_threshold

        # Method selection
        self.use_clip = use_clip and CLIP_AVAILABLE
        self.use_hash = use_hash and IMAGEHASH_AVAILABLE
        self.use_orb = use_orb
        self.use_sift = use_sift

        # Storage for features
        self.image_features = {}
        self.image_hashes = {}
        self.image_orb_features = {}
        self.image_sift_features = {}
        self.image_paths = []

        # Initialize CLIP model if enabled
        if self.use_clip:
            try:
                print(f"Loading CLIP model: {self.clip_model_name}...")
                self.device = self._select_device(device)
                self.clip_model, self.clip_preprocess = clip.load(self.clip_model_name, device=self.device)
                print(f"CLIP model {self.clip_model_name} loaded on {self.device}")
            except Exception as e:
                print(f"Warning: Failed to load CLIP model: {e}")
                self.use_clip = False

        # Initialize ORB detector if enabled
        if self.use_orb:
            try:
                self.orb = cv2.ORB_create(nfeatures=1000)
                print("ORB feature detector initialized")
            except Exception as e:
                print(f"Warning: Failed to initialize ORB: {e}")
                self.use_orb = False

        # Initialize SIFT detector if enabled
        if self.use_sift:
            try:
                self.sift = cv2.SIFT_create()
                print("SIFT feature detector initialized")
            except Exception as e:
                print(f"Warning: Failed to initialize SIFT: {e}")
                self.use_sift = False

        # Initialize FLANN matcher for feature matching
        if self.use_orb or self.use_sift:
            FLANN_INDEX_KDTREE = 1
            index_params = dict(algorithm=FLANN_INDEX_KDTREE, trees=5)
            search_params = dict(checks=50)
            self.flann = cv2.FlannBasedMatcher(index_params, search_params)

        # Print active methods
        active_methods = []
        if self.use_clip: active_methods.append("CLIP")
        if self.use_hash: active_methods.append("Perceptual Hash")
        if self.use_orb: active_methods.append("ORB")
        if self.use_sift: active_methods.append("SIFT")

        if not active_methods:
            raise RuntimeError("No similarity detection methods available or enabled")

        print(f"Active similarity methods: {', '.join(active_methods)}")
    
    def _select_device(self, device_preference: str) -> str:
        """
        Select the appropriate device based on preference and availability.
        
        Args:
            device_preference: User's device preference
            
        Returns:
            str: Selected device name
        """
        if device_preference == "cpu":
            return "cpu"
        elif device_preference == "auto":
            # Auto-select best available device
            if torch.backends.mps.is_available():
                return "mps"
            elif torch.cuda.is_available():
                return "cuda"
            else:
                return "cpu"
        elif device_preference in ["gpu", "mps"]:
            if torch.backends.mps.is_available():
                return "mps"
            else:
                print("Warning: MPS not available, falling back to CPU")
                return "cpu"
        elif device_preference == "cuda":
            if torch.cuda.is_available():
                return "cuda"
            else:
                print("Warning: CUDA not available, falling back to CPU")
                return "cpu"
        else:
            print(f"Warning: Unknown device '{device_preference}', using auto-selection")
            return self._select_device("auto")
    

    
    def extract_clip_features(self, image_path: str) -> np.ndarray:
        """
        Extract CLIP features for an image.

        Args:
            image_path: Path to the image file

        Returns:
            CLIP feature vector as numpy array
        """
        if not self.use_clip:
            return None

        try:
            with Image.open(image_path) as img:
                # Convert to RGB if necessary
                if img.mode != 'RGB':
                    img = img.convert('RGB')

                # Preprocess image for CLIP
                image_input = self.clip_preprocess(img).unsqueeze(0).to(self.device)

                # Extract features
                with torch.no_grad():
                    image_features = self.clip_model.encode_image(image_input)
                    # Normalize features
                    image_features = image_features / image_features.norm(dim=-1, keepdim=True)

                return image_features.cpu().numpy().flatten()
        except Exception as e:
            print(f"Error extracting CLIP features from {image_path}: {e}")
            return None

    def extract_perceptual_hash(self, image_path: str) -> Dict[str, str]:
        """
        Extract multiple perceptual hashes for an image.

        Args:
            image_path: Path to the image file

        Returns:
            Dictionary containing different hash types
        """
        if not self.use_hash:
            return None

        try:
            with Image.open(image_path) as img:
                # Convert to RGB if necessary
                if img.mode != 'RGB':
                    img = img.convert('RGB')

                # Calculate multiple hash types for better accuracy
                hashes = {
                    'ahash': str(imagehash.average_hash(img)),
                    'phash': str(imagehash.phash(img)),
                    'dhash': str(imagehash.dhash(img)),
                    'whash': str(imagehash.whash(img))
                }
                return hashes
        except Exception as e:
            print(f"Error extracting perceptual hash from {image_path}: {e}")
            return None

    def extract_orb_features(self, image_path: str) -> Tuple[np.ndarray, np.ndarray]:
        """
        Extract ORB features for an image.

        Args:
            image_path: Path to the image file

        Returns:
            Tuple of (keypoints, descriptors)
        """
        if not self.use_orb:
            return None, None

        try:
            # Read image in grayscale
            img = cv2.imread(image_path, cv2.IMREAD_GRAYSCALE)
            if img is None:
                return None, None

            # Detect ORB features
            keypoints, descriptors = self.orb.detectAndCompute(img, None)

            if descriptors is not None:
                return keypoints, descriptors
            else:
                return None, None
        except Exception as e:
            print(f"Error extracting ORB features from {image_path}: {e}")
            return None, None

    def extract_sift_features(self, image_path: str) -> Tuple[np.ndarray, np.ndarray]:
        """
        Extract SIFT features for an image.

        Args:
            image_path: Path to the image file

        Returns:
            Tuple of (keypoints, descriptors)
        """
        if not self.use_sift:
            return None, None

        try:
            # Read image in grayscale
            img = cv2.imread(image_path, cv2.IMREAD_GRAYSCALE)
            if img is None:
                return None, None

            # Detect SIFT features
            keypoints, descriptors = self.sift.detectAndCompute(img, None)

            if descriptors is not None:
                return keypoints, descriptors
            else:
                return None, None
        except Exception as e:
            print(f"Error extracting SIFT features from {image_path}: {e}")
            return None, None
    
    def compare_hashes(self, hash1: Dict[str, str], hash2: Dict[str, str]) -> float:
        """
        Compare perceptual hashes and return similarity score.

        Args:
            hash1: First image's hashes
            hash2: Second image's hashes

        Returns:
            Similarity score (0.0-1.0, higher = more similar)
        """
        if not hash1 or not hash2:
            return 0.0

        similarities = []
        for hash_type in ['ahash', 'phash', 'dhash', 'whash']:
            if hash_type in hash1 and hash_type in hash2:
                # Calculate hamming distance
                h1 = imagehash.hex_to_hash(hash1[hash_type])
                h2 = imagehash.hex_to_hash(hash2[hash_type])
                distance = h1 - h2  # Hamming distance

                # Convert to similarity (lower distance = higher similarity)
                max_distance = len(hash1[hash_type]) * 4  # 4 bits per hex char
                similarity = 1.0 - (distance / max_distance)
                similarities.append(similarity)

        # Return average similarity across all hash types
        return np.mean(similarities) if similarities else 0.0

    def compare_features(self, desc1: np.ndarray, desc2: np.ndarray, method: str = 'orb') -> float:
        """
        Compare feature descriptors and return match ratio.

        Args:
            desc1: First image's descriptors
            desc2: Second image's descriptors
            method: Feature type ('orb' or 'sift')

        Returns:
            Match ratio (0.0-1.0, higher = more similar)
        """
        if desc1 is None or desc2 is None:
            return 0.0

        if len(desc1) < 2 or len(desc2) < 2:
            return 0.0

        try:
            # Convert to float32 for FLANN
            if desc1.dtype != np.float32:
                desc1 = desc1.astype(np.float32)
            if desc2.dtype != np.float32:
                desc2 = desc2.astype(np.float32)

            # Find matches using FLANN
            matches = self.flann.knnMatch(desc1, desc2, k=2)

            # Apply Lowe's ratio test
            good_matches = []
            for match_pair in matches:
                if len(match_pair) == 2:
                    m, n = match_pair
                    if m.distance < 0.7 * n.distance:
                        good_matches.append(m)

            # Calculate match ratio
            total_features = min(len(desc1), len(desc2))
            if total_features == 0:
                return 0.0

            match_ratio = len(good_matches) / total_features
            return min(match_ratio, 1.0)  # Cap at 1.0

        except Exception as e:
            print(f"Error comparing {method} features: {e}")
            return 0.0

    def calculate_combined_similarity(self, path1: str, path2: str) -> float:
        """
        Calculate combined similarity score using all enabled methods.

        Args:
            path1: First image path
            path2: Second image path

        Returns:
            Combined similarity score (0.0-1.0)
        """
        similarities = []
        weights = []

        # CLIP similarity (semantic)
        if self.use_clip and path1 in self.image_features and path2 in self.image_features:
            clip_sim = cosine_similarity([self.image_features[path1]], [self.image_features[path2]])[0][0]
            similarities.append(clip_sim)
            weights.append(0.4)  # Higher weight for semantic similarity

        # Perceptual hash similarity (structural)
        if self.use_hash and path1 in self.image_hashes and path2 in self.image_hashes:
            hash_sim = self.compare_hashes(self.image_hashes[path1], self.image_hashes[path2])
            similarities.append(hash_sim)
            weights.append(0.3)

        # ORB feature similarity (keypoints)
        if self.use_orb and path1 in self.image_orb_features and path2 in self.image_orb_features:
            orb_sim = self.compare_features(
                self.image_orb_features[path1][1],
                self.image_orb_features[path2][1],
                'orb'
            )
            similarities.append(orb_sim)
            weights.append(0.2)

        # SIFT feature similarity (scale-invariant)
        if self.use_sift and path1 in self.image_sift_features and path2 in self.image_sift_features:
            sift_sim = self.compare_features(
                self.image_sift_features[path1][1],
                self.image_sift_features[path2][1],
                'sift'
            )
            similarities.append(sift_sim)
            weights.append(0.1)

        if not similarities:
            return 0.0

        # Calculate weighted average
        weighted_sum = sum(s * w for s, w in zip(similarities, weights))
        total_weight = sum(weights)

        return weighted_sum / total_weight if total_weight > 0 else 0.0

    def scan_directory(self, directory: str, extensions: List[str] = None) -> List[str]:
        """
        Scan directory for image files.
        
        Args:
            directory: Directory to scan
            extensions: List of file extensions to include
            
        Returns:
            List of image file paths
        """
        if extensions is None:
            extensions = ['.jpg', '.jpeg', '.png', '.bmp', '.tiff', '.tif', '.gif', '.webp']
        
        image_files = []
        for ext in extensions:
            pattern = os.path.join(directory, f"**/*{ext}")
            image_files.extend(glob.glob(pattern, recursive=True))
            pattern = os.path.join(directory, f"**/*{ext.upper()}")
            image_files.extend(glob.glob(pattern, recursive=True))
        
        return sorted(list(set(image_files)))
    
    def find_similar_images(self, image_paths: List[str]) -> Dict[str, List[str]]:
        """
        Find groups of similar images using multiple enhanced methods.

        Args:
            image_paths: List of image file paths

        Returns:
            Dictionary mapping representative image to list of similar images
        """
        print(f"Analyzing {len(image_paths)} images for similarity using enhanced methods...")
        return self._find_similar_images_enhanced(image_paths)
    
    def _find_similar_images_enhanced(self, image_paths: List[str]) -> Dict[str, List[str]]:
        """
        Find similar images using enhanced multi-method approach.
        """
        print("Using enhanced multi-method similarity detection...")

        # Extract features for all images using enabled methods
        valid_images = []

        for i, path in enumerate(image_paths):
            print(f"Processing image {i+1}/{len(image_paths)}: {os.path.basename(path)}")

            valid = False

            # Extract CLIP features
            if self.use_clip:
                features = self.extract_clip_features(path)
                if features is not None:
                    self.image_features[path] = features
                    valid = True

            # Extract perceptual hashes
            if self.use_hash:
                hashes = self.extract_perceptual_hash(path)
                if hashes is not None:
                    self.image_hashes[path] = hashes
                    valid = True

            # Extract ORB features
            if self.use_orb:
                kp, desc = self.extract_orb_features(path)
                if desc is not None:
                    self.image_orb_features[path] = (kp, desc)
                    valid = True

            # Extract SIFT features
            if self.use_sift:
                kp, desc = self.extract_sift_features(path)
                if desc is not None:
                    self.image_sift_features[path] = (kp, desc)
                    valid = True

            if valid:
                valid_images.append(path)
            else:
                print(f"Skipping invalid image: {path}")

        if len(valid_images) < 2:
            print("Not enough valid images for similarity comparison")
            return {}

        print(f"Successfully processed {len(valid_images)} images")

        # Group similar images using combined similarity
        groups = defaultdict(list)
        processed = set()

        print(f"Total valid images: {len(valid_images)}")
        print(f"Starting enhanced grouping...")

        # Define minimum similarity threshold (adaptive based on available methods)
        min_similarity = 0.6  # Base threshold
        if self.use_clip:
            min_similarity = max(min_similarity, self.clip_threshold * 0.8)  # Slightly lower for combined approach

        # Process all images
        for i, img1_path in enumerate(valid_images):
            if img1_path in processed:
                continue

            # Start a new group with this image
            current_group = [img1_path]
            processed.add(img1_path)

            # Find similar images
            for j, img2_path in enumerate(valid_images):
                if i == j or img2_path in processed:
                    continue

                # Calculate combined similarity
                similarity = self.calculate_combined_similarity(img1_path, img2_path)

                if similarity >= min_similarity:
                    current_group.append(img2_path)
                    processed.add(img2_path)

            # Include groups (both single and multi-image)
            if len(current_group) > 1:
                groups[img1_path] = current_group
                # Calculate average similarity for reporting
                similarities = []
                for img in current_group[1:]:
                    sim = self.calculate_combined_similarity(img1_path, img)
                    similarities.append(sim)
                avg_similarity = np.mean(similarities) if similarities else 0.0
                print(f"Found group of {len(current_group)} similar images (avg similarity: {avg_similarity:.3f}, representative: {os.path.basename(img1_path)})")
            else:
                # Single-image groups
                groups[img1_path] = current_group
                print(f"Created single-image group for: {os.path.basename(img1_path)}")

        print(f"Total groups created: {len(groups)}")
        return dict(groups)
    

    
    def create_batches(self, similar_groups: Dict[str, List[str]], batch_size: int = 4) -> List[List[str]]:
        """
        Create batches of images from similar groups based on similarity.
        Each batch contains similar images with a maximum of batch_size images.
        
        Args:
            similar_groups: Dictionary of similar image groups
            batch_size: Maximum number of images per batch (used as limit, not target)
            
        Returns:
            List of batches, each containing similar images (max batch_size per batch)
        """
        batches = []
        
        print(f"Creating batches from {len(similar_groups)} groups with max batch size {batch_size}")
        total_images = sum(len(group) for group in similar_groups.values())
        print(f"Total images to be processed: {total_images}")
        
        for group in similar_groups.values():
            # If group has more than batch_size images, split into multiple batches
            # but keep similar images together
            if len(group) > batch_size:
                # Split large groups into smaller batches of max batch_size
                for i in range(0, len(group), batch_size):
                    batch = group[i:i + batch_size]
                    batches.append(batch)
                    print(f"Created batch {len(batches)} with {len(batch)} similar images (split from larger group)")
            else:
                # Use the entire group as one batch
                # Include single-image groups as well to ensure all images are processed
                batches.append(group)
                if len(group) == 1:
                    print(f"Created batch {len(batches)} with 1 image (will be preserved as-is)")
                else:
                    print(f"Created batch {len(batches)} with {len(group)} images")
        
        total_batched_images = sum(len(batch) for batch in batches)
        print(f"Total images in batches: {total_batched_images}")
        print(f"Created {len(batches)} batches total")
        
        return batches
    
    def process_directory(self, input_dir: str, output_dir: str, batch_size: int = 4, 
                         target_size: Optional[Tuple[int, int]] = (300, 300), layout_mode: str = "grid", 
                         spacing: int = 15, background_color: str = "white", quality: int = 95,
                         clip_threshold: float = None, generate_master_file: bool = False):
        """
        Process a directory to find variations and create combined images.
        
        Args:
            input_dir: Input directory containing images
            output_dir: Output directory for combined images
            batch_size: Number of images per batch
            target_size: Target size for individual images (width, height), or None for dynamic sizing
            layout_mode: Layout mode ('grid', 'horizontal', 'vertical')
            spacing: Spacing between images in pixels
            background_color: Background color for combined images
            quality: JPEG quality (70-100)
            clip_threshold: Override clip_threshold for this operation
            generate_master_file: Whether to generate a master text file with all batch info
        """
        # Override settings if provided
        if clip_threshold is not None:
            original_clip_threshold = self.clip_threshold
            self.clip_threshold = clip_threshold
        # Create output directory
        os.makedirs(output_dir, exist_ok=True)
        
        # Scan for images
        print(f"Scanning directory: {input_dir}")
        image_paths = self.scan_directory(input_dir)
        
        if not image_paths:
            print("No images found in the specified directory")
            return []
        
        print(f"Found {len(image_paths)} image files")
        
        # Track all processed images to handle unprocessed ones later
        all_processed_images = set()
        
        # Find similar images
        similar_groups = self.find_similar_images(image_paths)
        
        if not similar_groups:
            print("No similar image groups found")
            # Don't return yet, process individual images below
        else:
            print(f"Found {len(similar_groups)} groups of similar images")
        
        # Create batches
        batches = self.create_batches(similar_groups, batch_size)
        
        if not batches:
            print("No valid batches created")
            # Don't return yet, process individual images below
        else:
            print(f"Created {len(batches)} batches for processing")
        
        # Combine images in each batch
        combiner = ImageCombiner(spacing=spacing, background_color=background_color)
        
        batch_counter = 0
        
        for i, batch in enumerate(batches, 1):
            try:
                print(f"\nProcessing batch {i}/{len(batches)} ({len(batch)} images)...")
                
                # Add all images in this batch to the processed set
                for img_path in batch:
                    all_processed_images.add(img_path)
                
                # For single-image batches, preserve the image as-is without combining
                if len(batch) == 1:
                    img_path = batch[0]
                    batch_counter += 1
                    
                    # Save image with original format, maintaining exact same image and size
                    original_format = os.path.splitext(img_path)[1].lower()
                    if original_format in ['.png', '.gif', '.webp', '.tiff', '.tif'] or background_color.lower() == "transparent":
                        output_filename = f"variation_batch_{batch_counter:03d}{original_format}"
                    else:
                        output_filename = f"variation_batch_{batch_counter:03d}.jpg"
                    
                    output_path = os.path.join(output_dir, output_filename)
                    
                    # Copy the original file directly to preserve the exact image with its original size
                    shutil.copy2(img_path, output_path)
                    
                    print(f"Saved individual image as-is: {output_filename} (original size preserved)")
                    
                    # Create a text file listing the source image
                    info_filename = f"variation_batch_{batch_counter:03d}_sources.txt"
                    info_path = os.path.join(output_dir, info_filename)
                    with open(info_path, 'w') as f:
                        f.write(f"Batch {batch_counter} - Source Image:\n")
                        f.write("=" * 30 + "\n")
                        f.write(f"1. {os.path.relpath(img_path, input_dir)}\n")
                    
                    continue  # Skip the rest of the loop for single images
                
                # For multi-image batches, combine them as before
                # Load images
                images = combiner.load_images(batch)
                
                if not images:
                    print(f"Failed to load images for batch {i}")
                    continue
                
                # Resize to uniform size
                images = combiner.resize_images_uniform(images, target_size=target_size)
                
                # Combine images based on layout mode
                if layout_mode == "grid":
                    combined = combiner.combine_grid(images)  # Let it auto-determine optimal layout
                elif layout_mode == "horizontal":
                    combined = combiner.combine_horizontal(images)
                elif layout_mode == "vertical":
                    combined = combiner.combine_vertical(images)
                else:
                    combined = combiner.combine_grid(images)  # Default to grid with optimal layout
                
                # Save combined image
                # Use PNG for transparent backgrounds, JPG for others
                batch_counter += 1
                if background_color.lower() == "transparent":
                    output_filename = f"variation_batch_{batch_counter:03d}.png"
                    output_path = os.path.join(output_dir, output_filename)
                    combined.save(output_path)
                else:
                    output_filename = f"variation_batch_{batch_counter:03d}.jpg"
                    output_path = os.path.join(output_dir, output_filename)
                    
                    # Convert RGBA to RGB if needed for JPEG format
                    if combined.mode == 'RGBA':
                        # Create a white background and paste the RGBA image onto it
                        rgb_image = Image.new('RGB', combined.size, (255, 255, 255))
                        rgb_image.paste(combined, mask=combined.split()[-1])  # Use alpha channel as mask
                        rgb_image.save(output_path, quality=quality)
                    else:
                        combined.save(output_path, quality=quality)
                
                print(f"Saved: {output_filename} ({combined.size[0]}x{combined.size[1]})")
                
                # Create a text file listing the source images
                info_filename = f"variation_batch_{batch_counter:03d}_sources.txt"
                info_path = os.path.join(output_dir, info_filename)
                with open(info_path, 'w') as f:
                    f.write(f"Batch {batch_counter} - Source Images:\n")
                    f.write("=" * 30 + "\n")
                    for j, img_path in enumerate(batch, 1):
                        f.write(f"{j}. {os.path.relpath(img_path, input_dir)}\n")
                
            except Exception as e:
                print(f"Error processing batch {i}: {e}")
                continue
        
        # Process any unprocessed images individually (no similarity match)
        unprocessed = [path for path in image_paths if path not in all_processed_images]
        if unprocessed:
            print(f"\nProcessing {len(unprocessed)} remaining images with no similarity matches...")
            
            # Process each unprocessed image individually
            for i, img_path in enumerate(unprocessed):
                try:
                    # Process this single image
                    
                    # Since it's a single image with no matches, preserve it exactly as is
                    batch_counter += 1
                    
                    # Save image with original format, maintaining exact same image and size
                    original_format = os.path.splitext(img_path)[1].lower()
                    if original_format in ['.png', '.gif', '.webp', '.tiff', '.tif'] or background_color.lower() == "transparent":
                        output_filename = f"variation_batch_{batch_counter:03d}{original_format}"
                    else:
                        output_filename = f"variation_batch_{batch_counter:03d}.jpg"
                    
                    output_path = os.path.join(output_dir, output_filename)
                    
                    # Copy the original file directly to preserve the exact image with its original size
                    shutil.copy2(img_path, output_path)
                    
                    print(f"Saved individual image as-is: {output_filename} (original size preserved)")
                    
                    # Create a text file listing the source image
                    info_filename = f"variation_batch_{batch_counter:03d}_sources.txt"
                    info_path = os.path.join(output_dir, info_filename)
                    with open(info_path, 'w') as f:
                        f.write(f"Batch {batch_counter} - Source Image:\n")
                        f.write("=" * 30 + "\n")
                        f.write(f"1. {os.path.relpath(img_path, input_dir)}\n")
                        
                except Exception as e:
                    print(f"Error processing individual image {img_path}: {e}")
                    continue
        
        # Generate a master text file with all batch information if requested
        if generate_master_file:
            master_filename = "all_batches_info.txt"
            master_path = os.path.join(output_dir, master_filename)
            with open(master_path, 'w') as f:
                f.write(f"Image Variation Batches - Master File\n")
                f.write(f"Total images processed: {len(image_paths)}\n")
                f.write(f"Total batches created: {batch_counter}\n")
                f.write(f"Similarity threshold: {self.clip_threshold}\n")
                f.write(f"CLIP model: {self.clip_model_name}\n")
                f.write("=" * 60 + "\n\n")
                
                # Write info about each batch
                for i in range(1, batch_counter + 1):
                    batch_file_path = os.path.join(output_dir, f"variation_batch_{i:03d}_sources.txt")
                    if os.path.exists(batch_file_path):
                        f.write(f"Batch #{i:03d}\n")
                        f.write("-" * 30 + "\n")
                        
                        # Copy content from the individual batch file
                        with open(batch_file_path, 'r') as batch_file:
                            # Skip the first two lines (header)
                            next(batch_file)  # Skip "Batch X - Source Images:"
                            next(batch_file)  # Skip "===================="
                            
                            # Copy the rest of the file
                            for line in batch_file:
                                f.write(line)
                        
                        f.write("\n")
            
            print(f"Generated master text file: {master_filename}")
        
        # Restore original settings if they were overridden
        if clip_threshold is not None:
            self.clip_threshold = original_clip_threshold
            
        print(f"\nProcessing complete! Check the output directory: {output_dir}")
        print(f"Total batches created: {batch_counter}")
        return batches

def main():
    parser = argparse.ArgumentParser(description="Automatically find image variations using enhanced multi-method similarity detection")
    parser.add_argument("input_dir", help="Input directory containing images")
    parser.add_argument("-o", "--output", default="variation_batches", help="Output directory for combined images")
    parser.add_argument("-b", "--batch-size", type=int, default=4, help="Number of images per batch")

    # CLIP options
    parser.add_argument("--clip-threshold", type=float, default=0.85, help="CLIP similarity threshold (0.0-1.0, higher = more strict)")
    parser.add_argument("--clip-model", default="ViT-B/32", help="CLIP model to use (ViT-B/32, ViT-L/14, RN50, etc.)")
    parser.add_argument("--device", default="auto", choices=["auto", "cpu", "gpu", "mps", "cuda"], help="Device to use for processing")

    # Perceptual hash options
    parser.add_argument("--hash-threshold", type=int, default=10, help="Perceptual hash threshold (lower = more strict)")

    # Feature matching options
    parser.add_argument("--orb-threshold", type=float, default=0.7, help="ORB feature match threshold (0.0-1.0, higher = more strict)")
    parser.add_argument("--sift-threshold", type=float, default=0.7, help="SIFT feature match threshold (0.0-1.0, higher = more strict)")

    # Method selection
    parser.add_argument("--disable-clip", action="store_true", help="Disable CLIP semantic similarity")
    parser.add_argument("--disable-hash", action="store_true", help="Disable perceptual hashing")
    parser.add_argument("--disable-orb", action="store_true", help="Disable ORB feature matching")
    parser.add_argument("--enable-sift", action="store_true", help="Enable SIFT feature matching (slower but more accurate)")

    # Other options
    parser.add_argument("--extensions", nargs="+", help="File extensions to include (e.g., .jpg .png)")
    parser.add_argument("--master-file", action="store_true", help="Generate a master text file with all batch information")

    args = parser.parse_args()

    if not os.path.isdir(args.input_dir):
        print(f"Error: Input directory '{args.input_dir}' does not exist")
        sys.exit(1)

    # Create enhanced variation finder
    try:
        finder = ImageVariationFinder(
            clip_threshold=args.clip_threshold,
            clip_model_name=args.clip_model,
            device=args.device,
            hash_threshold=args.hash_threshold,
            orb_threshold=args.orb_threshold,
            sift_threshold=args.sift_threshold,
            use_clip=not args.disable_clip,
            use_hash=not args.disable_hash,
            use_orb=not args.disable_orb,
            use_sift=args.enable_sift
        )
    except Exception as e:
        print(f"Error initializing similarity detector: {e}")
        sys.exit(1)

    # Process directory
    finder.process_directory(
        input_dir=args.input_dir,
        output_dir=args.output,
        batch_size=args.batch_size,
        generate_master_file=args.master_file
    )

if __name__ == "__main__":
    main()